import fitz  # PyMuPDF
import csv
import re
from pathlib import Path
from datetime import datetime

pdf_dir = Path(__file__).parent / "2024"
timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
output_dir = Path("outputs/" + timestamp)
output_dir.mkdir(exist_ok=True)
csv_path = output_dir / f"electors_master_{timestamp}.csv"
error_log_path = output_dir / f"electors_errors_{timestamp}.log"

csv_file = open(csv_path, mode="w", newline="", encoding="utf-8")
csv_writer = csv.writer(csv_file)
csv_writer.writerow(["Polling Division", "Surname", "Given Names", "Address", "Serial Number", "Source File"])

error_log = open(error_log_path, mode="w", encoding="utf-8")

# Create unmatched CSV file and writer
unmatched_csv_path = output_dir / f"unmatched_{timestamp}.csv"
unmatched_csv_file = open(unmatched_csv_path, mode="w", newline="", encoding="utf-8")
unmatched_writer = csv.writer(unmatched_csv_file)
unmatched_writer.writerow(["Source File", "Polling Division", "Unmatched Line"])

# Polling division pattern: 4 consecutive digits not part of a larger number
pd_pattern = re.compile(r"(?<!\d)(\d{4})(?!\d)")

pdf_files = list(pdf_dir.glob("*.pdf"))
print(f"Found {len(pdf_files)} PDF files in {pdf_dir}")

# Initialise summary variables
total_lines = 0
matched_lines = 0
unmatched_lines = 0

for pdf_file in pdf_files:
    try:
        with fitz.open(pdf_file) as doc:
            text_pages = []
            for i, page in enumerate(doc):
                blocks = page.get_text("dict")["blocks"]
                lines = []
                for b in blocks:
                    if "lines" in b:
                        for line in b["lines"]:
                            text_line = " ".join(span["text"] for span in line["spans"]).strip()
                            if text_line:
                                lines.append(text_line)
                page_text = "\n".join(lines)
                text_pages.append(page_text)
            print(f"Extracted {len(text_pages)} non-empty pages from {pdf_file.name}")
            text = "\n".join(text_pages)

        polling_division = "UNKNOWN"
        pd_match = pd_pattern.search(text)
        if pd_match:
            polling_division = pd_match.group(1)


        for line in text.splitlines():
            line = line.strip()
            if not line or line.startswith("EMAN") or "PAGE" in line.upper():
                continue
            total_lines += 1

            # Try to match "Surname, Given Names    Address    Serial" format
            parts = re.split(r'\s{2,}', line)
            if len(parts) == 3 and ',' in parts[0] and re.match(r"[0-9]{2}[A-Z]{2}", parts[2]):
                name_part = parts[0].strip()
                address = parts[1].strip()
                serial = parts[2].strip()

                surname, given_names = [p.strip().title() for p in name_part.split(',', 1)]
                address = re.sub(r"\s+", " ", address.title())
                row = [polling_division, surname, given_names, address, serial, pdf_file.name]
                csv_writer.writerow(row)

                matched_lines += 1
                if matched_lines <= 3:
                    print("Sample parsed entry:", row)
            else:
                unmatched_lines += 1
                unmatched_writer.writerow([pdf_file.name, polling_division, line])
    except Exception as e:
        error_log.write(f"FAILED TO PROCESS {pdf_file.name}: {str(e)}\n")

print(f"--- Parsing Summary ---")
print(f"Total lines processed: {total_lines}")
print(f"Matched entries: {matched_lines}")
print(f"Unmatched lines: {unmatched_lines}")
if total_lines > 0:
    print(f"Match rate: {100 * matched_lines / total_lines:.2f}%")
else:
    print("Match rate: N/A (no lines processed)")

csv_file.close()
error_log.close()
unmatched_csv_file.close()